import type {
	DatabaseAdapter,
	DatabaseConfig,
	Repository,
	User,
	Organization,
	Listing,
	Agent,
	Lead,
	Session,
	CreateUserInput,
	UpdateUserInput,
	CreateOrganizationInput,
	UpdateOrganizationInput,
	CreateListingInput,
	UpdateListingInput,
	CreateAgentInput,
	UpdateAgentInput,
	CreateLeadInput,
	UpdateLeadInput,
	CreateSessionInput,
	UpdateSessionInput
} from '@/lib/types'

// Interface for PrismaClient methods we need
interface PrismaClientLike {
	$connect(): Promise<void>
	$disconnect(): Promise<void>
	$transaction<T>(fn: () => Promise<T>): Promise<T>
}

// Database Adapter Factory
export class DatabaseAdapterFactory {
	static create(config: DatabaseConfig): DatabaseAdapter {
		switch (config.type) {
			case 'postgresql':
				return new PostgreSQLAdapter(config)
			case 'mongodb':
				return new MongoDBAdapter(config)
			default:
				throw new Error(`Unsupported database type: ${config.type}`)
		}
	}
}

// Abstract Base Adapter
export abstract class BaseAdapter implements DatabaseAdapter {
	protected config: DatabaseConfig
	protected connected = false

	constructor(config: DatabaseConfig) {
		this.config = config
	}

	abstract connect(): Promise<void>
	abstract disconnect(): Promise<void>
	abstract transaction<T>(fn: () => Promise<T>): Promise<T>

	// Repository factory methods
	abstract getUserRepository(): Repository<User, CreateUserInput, UpdateUserInput>
	abstract getOrganizationRepository(): Repository<Organization, CreateOrganizationInput, UpdateOrganizationInput>
	abstract getListingRepository(): Repository<Listing, CreateListingInput, UpdateListingInput>
	abstract getAgentRepository(): Repository<Agent, CreateAgentInput, UpdateAgentInput>
	abstract getLeadRepository(): Repository<Lead, CreateLeadInput, UpdateLeadInput>
	abstract getSessionRepository(): Repository<Session, CreateSessionInput, UpdateSessionInput>

	protected ensureConnected(): void {
		if (!this.connected) {
			throw new Error('Database not connected. Call connect() first.')
		}
	}
}

// PostgreSQL Adapter (Prisma-based)
export class PostgreSQLAdapter extends BaseAdapter {
	private prisma: PrismaClientLike | null = null

	async connect(): Promise<void> {
		try {
			// Initialize Prisma client
			const { PrismaClient } = await import('@prisma/client')
			this.prisma = new PrismaClient({
				datasources: {
					db: {
						url: this.config.connectionString
					}
				},
				...this.config.options
			}) as PrismaClientLike

			// Test connection
			await this.prisma.$connect()
			this.connected = true
		} catch (error) {
			throw new Error(`Failed to connect to PostgreSQL: ${error}`)
		}
	}

	async disconnect(): Promise<void> {
		if (this.prisma) {
			await this.prisma.$disconnect()
			this.connected = false
		}
	}

	async transaction<T>(fn: () => Promise<T>): Promise<T> {
		this.ensureConnected()
		if (!this.prisma) {
			throw new Error('Prisma client not initialized')
		}
		return this.prisma.$transaction(fn)
	}

	getUserRepository(): Repository<User, CreateUserInput, UpdateUserInput> {
		return new PostgreSQLUserRepository(this.prisma)
	}

	getOrganizationRepository(): Repository<Organization, CreateOrganizationInput, UpdateOrganizationInput> {
		return new PostgreSQLOrganizationRepository(this.prisma)
	}

	getListingRepository(): Repository<Listing, CreateListingInput, UpdateListingInput> {
		return new PostgreSQLListingRepository(this.prisma)
	}

	getAgentRepository(): Repository<Agent, CreateAgentInput, UpdateAgentInput> {
		return new PostgreSQLAgentRepository(this.prisma)
	}

	getLeadRepository(): Repository<Lead, CreateLeadInput, UpdateLeadInput> {
		return new PostgreSQLLeadRepository(this.prisma)
	}

	getSessionRepository(): Repository<Session, CreateSessionInput, UpdateSessionInput> {
		return new PostgreSQLSessionRepository(this.prisma)
	}
}

// MongoDB Adapter
export class MongoDBAdapter extends BaseAdapter {
	private client: unknown // Will be MongoClient
	private db: any // Will be Db

	async connect(): Promise<void> {
		try {
			const { MongoClient } = await import('mongodb')
			this.client = new MongoClient(this.config.connectionString, this.config.options)
			await this.client.connect()

			// Extract database name from connection string
			const dbName = new URL(this.config.connectionString).pathname.slice(1)
			this.db = this.client.db(dbName)

			this.connected = true
		} catch (error) {
			throw new Error(`Failed to connect to MongoDB: ${error}`)
		}
	}

	async disconnect(): Promise<void> {
		if (this.client) {
			await this.client.close()
			this.connected = false
		}
	}

	async transaction<T>(fn: () => Promise<T>): Promise<T> {
		this.ensureConnected()
		const session = this.client.startSession()

		try {
			return await session.withTransaction(fn)
		} finally {
			await session.endSession()
		}
	}

	getUserRepository(): Repository<User, CreateUserInput, UpdateUserInput> {
		return new MongoDBUserRepository(this.db)
	}

	getOrganizationRepository(): Repository<Organization, CreateOrganizationInput, UpdateOrganizationInput> {
		return new MongoDBOrganizationRepository(this.db)
	}

	getListingRepository(): Repository<Listing, CreateListingInput, UpdateListingInput> {
		return new MongoDBListingRepository(this.db)
	}

	getAgentRepository(): Repository<Agent, CreateAgentInput, UpdateAgentInput> {
		return new MongoDBAgentRepository(this.db)
	}

	getLeadRepository(): Repository<Lead, CreateLeadInput, UpdateLeadInput> {
		return new MongoDBLeadRepository(this.db)
	}

	getSessionRepository(): Repository<Session, CreateSessionInput, UpdateSessionInput> {
		return new MongoDBSessionRepository(this.db)
	}
}

// Import repository implementations (to be created)
import { PostgreSQLUserRepository } from './repositories/postgresql/user'
import { PostgreSQLOrganizationRepository } from './repositories/postgresql/organization'
import { PostgreSQLListingRepository } from './repositories/postgresql/listing'
import { PostgreSQLAgentRepository } from './repositories/postgresql/agent'
import { PostgreSQLLeadRepository } from './repositories/postgresql/lead'
import { PostgreSQLSessionRepository } from './repositories/postgresql/session'

import { MongoDBUserRepository } from './repositories/mongodb/user'
import { MongoDBOrganizationRepository } from './repositories/mongodb/organization'
import { MongoDBListingRepository } from './repositories/mongodb/listing'
import { MongoDBAgentRepository } from './repositories/mongodb/agent'
import { MongoDBLeadRepository } from './repositories/mongodb/lead'
import { MongoDBSessionRepository } from './repositories/mongodb/session'

// Database instance singleton
let databaseInstance: DatabaseAdapter | null = null

export function getDatabaseAdapter(): DatabaseAdapter {
	if (!databaseInstance) {
		const config: DatabaseConfig = {
			type: (process.env.DATABASE_TYPE as 'postgresql' | 'mongodb') || 'postgresql',
			connectionString: process.env.DATABASE_URL || '',
			options: {
				// PostgreSQL options
				...(process.env.DATABASE_TYPE === 'postgresql' && {
					log: process.env.NODE_ENV === 'development' ? ['query', 'error'] : ['error']
				}),
				// MongoDB options
				...(process.env.DATABASE_TYPE === 'mongodb' && {
					maxPoolSize: 10,
					serverSelectionTimeoutMS: 5000,
					socketTimeoutMS: 45000
				})
			}
		}

		databaseInstance = DatabaseAdapterFactory.create(config)
	}

	return databaseInstance
}

export async function initializeDatabase(): Promise<void> {
	const adapter = getDatabaseAdapter()
	await adapter.connect()
}

export async function closeDatabase(): Promise<void> {
	if (databaseInstance) {
		await databaseInstance.disconnect()
		databaseInstance = null
	}
}
